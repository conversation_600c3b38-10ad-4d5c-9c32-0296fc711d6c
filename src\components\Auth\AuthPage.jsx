import { useState } from 'react';
import Login from './Login';
import Register from './Register';
import { useAuth } from '../../context/AuthContext';

const AuthPage = () => {
  const [isLogin, setIsLogin] = useState(true);
  const { login } = useAuth();

  const handleAuth = (token, user) => {
    login(token, user);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {isLogin ? (
        <div>
          <Login onLogin={handleAuth} />
          <div className="text-center mt-4">
            <p className="text-sm text-gray-600">
              Don't have an account?{' '}
              <button
                onClick={() => setIsLogin(false)}
                className="font-medium text-indigo-600 hover:text-indigo-500"
              >
                Sign up
              </button>
            </p>
          </div>
        </div>
      ) : (
        <div>
          <Register onRegister={handleAuth} />
          <div className="text-center mt-4">
            <p className="text-sm text-gray-600">
              Already have an account?{' '}
              <button
                onClick={() => setIsLogin(true)}
                className="font-medium text-indigo-600 hover:text-indigo-500"
              >
                Sign in
              </button>
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default AuthPage;
