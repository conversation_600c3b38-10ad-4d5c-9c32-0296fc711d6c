# ATMA Frontend - AI-Driven Talent Mapping Assessment

A modern React application for conducting comprehensive talent assessments using VIA Character Strengths, RIASEC Holland Codes, and Big Five Personality assessments.

## 🚀 Features

- **User Authentication**: JWT-based login/register system
- **Multi-Assessment Flow**: VIA Character Strengths, RIASEC Holland Codes, Big Five Inventory
- **Real-time Notifications**: WebSocket integration with polling fallback
- **Assessment Status Tracking**: Live progress monitoring
- **Results Dashboard**: Comprehensive results display with persona profiles
- **Responsive Design**: Mobile-first design with Tailwind CSS
- **Modern Tech Stack**: React + Vite + Tailwind + Axios + React Hook Form + Socket.IO

## 🛠️ Tech Stack

- **Frontend Framework**: React 18
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **HTTP Client**: Axios
- **Form Management**: React Hook Form
- **Routing**: React Router DOM
- **Real-time Communication**: Socket.IO Client
- **State Management**: React Context API

## 📋 Prerequisites

- Node.js 16+
- npm or yarn
- Backend API Gateway running on port 3000
- Notification Service running on port 3005

## 🚀 Quick Start

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env
   # Edit .env with your API endpoints if needed
   ```

3. **Start Development Server**
   ```bash
   npm run dev
   ```

4. **Open Browser**
   Navigate to `http://localhost:5173`

## 📊 Assessment Flow

1. **User Registration/Login** - JWT token authentication
2. **Assessment Process** - VIA (96 questions), RIASEC (60 questions), Big Five (44 questions)
3. **Scoring & Submission** - Frontend calculates scores, submits to API
4. **Status Monitoring** - Real-time WebSocket notifications with polling fallback
5. **Results Display** - Persona profiles and detailed scores

## 🔌 WebSocket Integration

Real-time notifications using Socket.IO:
- Analysis completion notifications
- Progress updates
- Error handling
- Automatic reconnection

## 🎨 UI Components

Reusable components built with Tailwind CSS:
- Loading spinners and progress bars
- Error messages and notifications
- Responsive forms and navigation
- Assessment question interfaces

## 🚀 Development Commands

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## 📁 Project Structure

```
src/
├── components/     # React components
│   ├── Auth/       # Authentication
│   ├── Assessment/ # Assessment flow
│   ├── Dashboard/  # Dashboard
│   ├── Layout/     # Layout components
│   ├── Results/    # Results display
│   └── UI/         # Reusable UI
├── context/        # Context providers
├── data/          # Assessment questions
├── hooks/         # Custom hooks
├── services/      # API services
├── utils/         # Utilities
└── config/        # Configuration
```

## 🔧 Configuration

Environment variables in `.env`:
```env
VITE_API_BASE_URL=http://localhost:3000
VITE_NOTIFICATION_URL=http://localhost:3005
VITE_APP_ENV=development
VITE_DEBUG=true
```

## 📄 License

Part of the ATMA (AI-Driven Talent Mapping Assessment) system.
